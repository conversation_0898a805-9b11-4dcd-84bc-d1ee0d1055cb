import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import {
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  ClockIcon,
  PaperAirplaneIcon,
  SparklesIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';

// Validation schema
const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

type ContactFormData = z.infer<typeof contactSchema>;

export default function Contact() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
  });

  const onSubmit = async (_data: ContactFormData) => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real app, you would send the form data to your backend
      // TODO: Send form data to backend API
      
      toast.success(t('contact.form.success'));
      reset();
    } catch (error) {
      toast.error(t('contact.form.error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: MapPinIcon,
      label: t('contact.info.address'),
      value: t('contact.info.address'),
    },
    {
      icon: PhoneIcon,
      label: isRTL ? 'الهاتف' : 'Phone',
      value: t('contact.info.phone'),
    },
    {
      icon: EnvelopeIcon,
      label: isRTL ? 'البريد الإلكتروني' : 'Email',
      value: t('contact.info.email'),
    },
    {
      icon: ClockIcon,
      label: isRTL ? 'ساعات العمل' : 'Working Hours',
      value: t('contact.info.hours'),
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="contact"
      ref={ref}
      className="section-padding relative overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(0, 122, 61, 0.15) 0%, transparent 60%),
          radial-gradient(circle at ${100 - mousePosition.x}% ${100 - mousePosition.y}%, rgba(206, 17, 38, 0.12) 0%, transparent 60%),
          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large Glass Orbs with Syrian Colors */}
        <motion.div
          animate={{
            y: [-30, 30, -30],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 22, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 right-10 w-32 h-32 rounded-full opacity-20"
          style={{
            background: 'rgba(0, 122, 61, 0.1)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: '1px solid rgba(0, 122, 61, 0.2)',
            boxShadow: '0 8px 32px rgba(0, 122, 61, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [40, -40, 40],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 28, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-40 left-20 w-24 h-24 rounded-full opacity-15"
          style={{
            background: 'rgba(206, 17, 38, 0.1)',
            backdropFilter: 'blur(15px)',
            WebkitBackdropFilter: 'blur(15px)',
            border: '1px solid rgba(206, 17, 38, 0.2)',
            boxShadow: '0 8px 32px rgba(206, 17, 38, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [-25, 25, -25],
            x: [-10, 10, -10],
            rotate: [0, 90, 180]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-1/2 right-1/4 w-40 h-40 rounded-full opacity-10"
          style={{
            background: 'rgba(124, 58, 237, 0.1)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            border: '1px solid rgba(124, 58, 237, 0.2)',
            boxShadow: '0 8px 32px rgba(124, 58, 237, 0.1)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 10 + i * 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.7
            }}
            className="absolute w-2 h-2 bg-white rounded-full"
            style={{
              left: `${5 + (i * 10)}%`,
              top: `${15 + (i * 7)}%`,
              filter: 'blur(1px)'
            }}
          />
        ))}
      </div>

      <div className="container mx-auto container-padding relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mb-20"
        >
          {/* Enhanced Icon with Glass Effect */}
          <motion.div
            variants={itemVariants}
            className="relative mb-12"
          >
            <motion.div
              className="w-24 h-24 mx-auto rounded-full flex items-center justify-center relative z-10"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ duration: 0.3 }}
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%)',
                backdropFilter: 'blur(20px)',
                WebkitBackdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
              }}
            >
              <ChatBubbleLeftRightIcon className="w-12 h-12 text-white" />
              <SparklesIcon className="w-6 h-6 text-white absolute -top-2 -right-2 animate-pulse" />
            </motion.div>
          </motion.div>

          {/* Enhanced Title with Glass Container */}
          <motion.div
            variants={itemVariants}
            className="relative mb-8"
          >
            <motion.h2
              className="heading-lg text-white mb-4 relative z-10 px-8 py-4 text-arabic-premium"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {t('contact.title')}
            </motion.h2>

            {/* Glass backdrop for title */}
            <div
              className="absolute inset-0 rounded-2xl opacity-30"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%)',
                backdropFilter: 'blur(30px)',
                WebkitBackdropFilter: 'blur(30px)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'
              }}
            />
          </motion.div>

          {/* Enhanced Subtitle */}
          <motion.p
            variants={itemVariants}
            className="text-xl text-white/90 max-w-4xl mx-auto text-center leading-relaxed text-arabic px-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {t('contact.subtitle')}
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 max-w-7xl mx-auto">
          {/* Enhanced Contact Information */}
          <motion.div
            variants={itemVariants}
            initial="hidden"
            animate={inView ? 'visible' : 'hidden'}
            className="space-y-8"
          >
            <div
              className="p-8 rounded-3xl"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                backdropFilter: 'blur(30px)',
                WebkitBackdropFilter: 'blur(30px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <h3 className="heading-sm mb-8 text-white text-arabic-premium">
                {isRTL ? 'معلومات التواصل' : 'Contact Information'}
              </h3>

              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    className="flex items-start gap-4 p-4 rounded-xl transition-all duration-300 hover:bg-white/5"
                    whileHover={{ scale: 1.02, x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div
                      className="w-14 h-14 rounded-xl flex items-center justify-center flex-shrink-0"
                      style={{
                        background: `rgba(${index === 0 ? '206, 17, 38' : index === 1 ? '0, 122, 61' : '124, 58, 237'}, 0.1)`,
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.2)'
                      }}
                    >
                      <info.icon className="w-7 h-7 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-2 text-arabic-premium">
                        {info.label}
                      </h4>
                      <p className="text-white/80 text-arabic">
                        {info.value}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Enhanced Map Placeholder */}
            <motion.div
              className="rounded-3xl h-64 flex items-center justify-center relative overflow-hidden"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%)',
                backdropFilter: 'blur(20px)',
                WebkitBackdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
              }}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-center">
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <MapPinIcon className="w-16 h-16 text-white/60 mx-auto mb-4" />
                </motion.div>
                <p className="text-white/70 text-lg text-arabic-premium">
                  {isRTL ? 'خريطة الموقع' : 'Location Map'}
                </p>
                <p className="text-white/50 text-sm mt-2 text-arabic">
                  {isRTL ? 'قريباً' : 'Coming Soon'}
                </p>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Contact Form */}
          <motion.div
            variants={itemVariants}
            initial="hidden"
            animate={inView ? 'visible' : 'hidden'}
            className="relative"
          >
            <div
              className="p-8 rounded-3xl"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                backdropFilter: 'blur(30px)',
                WebkitBackdropFilter: 'blur(30px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <h3 className="heading-sm mb-8 text-white text-arabic-premium">
                {isRTL ? 'أرسل لنا رسالة' : 'Send us a Message'}
              </h3>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Enhanced Form Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Name */}
                  <div>
                    <label className="block text-sm font-medium text-white/90 mb-3 text-arabic">
                      {t('contact.form.name')}
                    </label>
                    <input
                      {...register('name')}
                      type="text"
                      className={`w-full px-4 py-3 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white/50 focus:outline-none transition-all duration-300 text-arabic ${
                        isRTL ? 'text-right' : 'text-left'
                      }`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.3)'
                      }}
                      disabled={isSubmitting}
                    />
                    {errors.name && (
                      <p className="text-red-300 text-sm mt-2 text-arabic">
                        {t('common.validation.required')}
                      </p>
                    )}
                  </div>

                  {/* Email */}
                  <div>
                    <label className="block text-sm font-medium text-white/90 mb-3 text-arabic">
                      {t('contact.form.email')}
                    </label>
                    <input
                      {...register('email')}
                      type="email"
                      className={`w-full px-4 py-3 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white/50 focus:outline-none transition-all duration-300 text-arabic ${
                        isRTL ? 'text-right' : 'text-left'
                      }`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.3)'
                      }}
                      disabled={isSubmitting}
                    />
                    {errors.email && (
                      <p className="text-red-300 text-sm mt-2 text-arabic">
                        {t('common.validation.email')}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Phone */}
                  <div>
                    <label className="block text-sm font-medium text-white/90 mb-3 text-arabic">
                      {t('contact.form.phone')}
                    </label>
                    <input
                      {...register('phone')}
                      type="tel"
                      className={`w-full px-4 py-3 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white/50 focus:outline-none transition-all duration-300 text-arabic ${
                        isRTL ? 'text-right' : 'text-left'
                      }`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.3)'
                      }}
                      disabled={isSubmitting}
                    />
                  </div>

                  {/* Subject */}
                  <div>
                    <label className="block text-sm font-medium text-white/90 mb-3 text-arabic">
                      {t('contact.form.subject')}
                    </label>
                    <input
                      {...register('subject')}
                      type="text"
                      className={`w-full px-4 py-3 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white/50 focus:outline-none transition-all duration-300 text-arabic ${
                        isRTL ? 'text-right' : 'text-left'
                      }`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.3)'
                      }}
                      disabled={isSubmitting}
                    />
                    {errors.subject && (
                      <p className="text-red-300 text-sm mt-2 text-arabic">
                        {t('common.validation.required')}
                      </p>
                    )}
                  </div>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-white/90 mb-3 text-arabic">
                    {t('contact.form.message')}
                  </label>
                  <textarea
                    {...register('message')}
                    rows={5}
                    className={`w-full px-4 py-3 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white/50 focus:outline-none transition-all duration-300 resize-none text-arabic ${
                      isRTL ? 'text-right' : 'text-left'
                    }`}
                    style={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      WebkitBackdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.3)'
                    }}
                    disabled={isSubmitting}
                  />
                  {errors.message && (
                    <p className="text-red-300 text-sm mt-2 text-arabic">
                      {t('common.validation.required')}
                    </p>
                  )}
                </div>

                {/* Enhanced Submit Button */}
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full font-bold px-8 py-4 rounded-xl transition-all duration-300 flex items-center justify-center gap-3 group text-arabic-premium"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%)',
                    color: '#1e293b',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  {isSubmitting ? (
                    <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <PaperAirplaneIcon className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
                  )}
                  {isSubmitting ? t('common.status.loading') : t('contact.form.submit')}
                  {!isSubmitting && <SparklesIcon className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />}
                </motion.button>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
