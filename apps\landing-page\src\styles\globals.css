@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for theming - Dark theme as default */
:root {
  /* Dark theme as default */
  --primary-bg: #0f172a;
  --secondary-bg: #1e293b;
  --accent-color: #0ea5e9;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --toast-bg: #1f2937;
  --toast-color: #f9fafb;
}

[data-theme="light"] {
  --primary-bg: #ffffff;
  --secondary-bg: #f8fafc;
  --accent-color: #0ea5e9;
  --text-primary: #1f2937;
  --text-secondary: #64748b;
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(0, 0, 0, 0.1);
  --toast-bg: #ffffff;
  --toast-color: #1f2937;
}

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-dark-900 text-gray-100 transition-colors duration-300;
    background: var(--primary-bg);
    color: var(--text-primary);
  }
  
  /* Enhanced Arabic font optimization */
  .font-arabic {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "ss01" 1, "ss02" 1;
    font-variant-ligatures: common-ligatures contextual;
    text-rendering: optimizeLegibility;
  }

  .font-cairo {
    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "ss01" 1;
  }

  .font-tajawal {
    font-family: var(--font-tajawal), 'Tajawal', 'Cairo', 'Noto Sans Arabic', system-ui, sans-serif;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }
  
  /* RTL specific styles */
  [dir="rtl"] {
    text-align: right;
  }
  
  [dir="rtl"] .ltr-content {
    direction: ltr;
    text-align: left;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* Component styles */
@layer components {
  /* Enhanced Button variants with glass effects */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 hover:scale-105;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .btn-secondary {
    @apply bg-white/10 hover:bg-white/20 text-primary-600 dark:text-primary-400 border border-white/20 hover:border-white/30 font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
  }

  .btn-outline {
    @apply border border-gray-300/30 hover:border-gray-400/50 text-gray-700 dark:text-gray-300 dark:border-gray-600/30 dark:hover:border-gray-500/50 font-medium px-6 py-3 rounded-xl transition-all duration-300 bg-white/5 hover:bg-white/10;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }

  /* Enhanced Glass effect buttons */
  .btn-glass {
    @apply bg-white/10 hover:bg-white/20 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .btn-glass:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  /* Enhanced glass button with premium effects */
  .glass-button-enhanced {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    -webkit-backdrop-filter: blur(32px);
    backdrop-filter: blur(32px);
    border: 2px solid rgba(255, 255, 255, 0.25);
    border-radius: 16px;
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .glass-button-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    transition: left 0.6s ease;
  }

  .glass-button-enhanced:hover::before {
    left: 100%;
  }

  .glass-button-enhanced:hover {
    transform: translateY(-3px) scale(1.03);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }

  /* Premium glass button for hero and main CTAs */
  .glass-button {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
    -webkit-backdrop-filter: blur(25px);
    backdrop-filter: blur(25px);
    border: 1.5px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glass-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, transparent 100%);
    transition: left 0.5s ease;
  }

  .glass-button:hover::before {
    left: 100%;
  }

  .glass-button:hover {
    transform: translateY(-2px) scale(1.02);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.10) 100%);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }

  /* Glass card for hero stats and feature cards */
  .glass-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.25);
  }
  
  /* Enhanced Card styles with glass effects */
  .card {
    @apply bg-white/80 dark:bg-gray-800/80 rounded-2xl shadow-lg border border-white/20 dark:border-gray-700/30 transition-all duration-300;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
  }

  .card-hover {
    @apply hover:shadow-xl hover:-translate-y-2 transform transition-all duration-300 hover:scale-105;
  }

  .card-glass {
    @apply rounded-2xl transition-all duration-300;
    background: rgba(255, 255, 255, 0.08);
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .card-glass:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  .card-premium {
    @apply rounded-3xl transition-all duration-500;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    -webkit-backdrop-filter: blur(30px);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .card-premium:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  
  /* Input styles */
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-all duration-200;
  }
  
  /* Section spacing */
  .section-padding {
    @apply py-16 lg:py-24;
  }
  
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  /* Enhanced Typography with premium styling - Dark theme optimized */
  .heading-hero {
    font-size: clamp(3.5rem, 12vw, 8rem);
    font-weight: 900;
    line-height: 0.85;
    letter-spacing: -0.04em;
    @apply font-cairo;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  }

  .heading-hero-mobile {
    font-size: clamp(2.5rem, 15vw, 4rem);
    font-weight: 800;
    line-height: 0.9;
    letter-spacing: -0.03em;
    @apply font-cairo;
  }

  .heading-xl {
    @apply text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight font-display;
  }

  .heading-lg {
    @apply text-3xl lg:text-4xl font-bold tracking-tight font-display;
  }

  .heading-md {
    @apply text-2xl lg:text-3xl font-semibold tracking-tight;
  }

  .heading-sm {
    @apply text-xl lg:text-2xl font-semibold;
  }

  /* Enhanced gradient text effects */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 via-secondary-500 to-primary-700 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
  }

  .gradient-text-premium {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 30%, #e2e8f0 60%, #cbd5e1 100%);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: premiumGradient 4s ease-in-out infinite;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  .text-glass {
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced Arabic typography */
  .text-arabic {
    @apply font-cairo;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "ss01" 1, "ss02" 1;
    font-variant-ligatures: common-ligatures contextual;
    text-rendering: optimizeLegibility;
  }

  .text-arabic-display {
    @apply font-cairo;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    font-weight: 700;
  }

  .text-arabic-premium {
    @apply font-cairo;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "ss01" 1;
    font-variant-ligatures: common-ligatures contextual;
    text-rendering: optimizeLegibility;
    color: var(--text-primary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Premium section headers with Syrian cultural elements */
  .section-header-premium {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 30%, #e2e8f0 60%, #cbd5e1 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }

  /* Syrian cultural accent elements */
  .syrian-accent-vertical {
    background: linear-gradient(180deg, #CE1126 0%, #007A3D 100%);
    border-radius: 9999px;
    box-shadow: 0 4px 8px rgba(206, 17, 38, 0.3);
  }

  /* Premium glass morphism cards */
  .glass-card-premium {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    -webkit-backdrop-filter: blur(25px);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
  }

  .glass-card-premium:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
  }

  /* Syrian gradient buttons */
  .btn-syrian-gradient {
    background: linear-gradient(135deg, #CE1126 0%, #007A3D 100%);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(206, 17, 38, 0.3);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
  }

  .btn-syrian-gradient:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 12px 35px rgba(206, 17, 38, 0.4);
  }

  /* Shimmer effect for buttons and cards */
  .shimmer-effect {
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: glassShimmer 1.5s ease-in-out infinite;
  }

  /* Popular badge styling */
  .popular-badge {
    background: linear-gradient(135deg, #CE1126 0%, #007A3D 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(206, 17, 38, 0.3);
    border-radius: 25px;
  }

  /* Tab navigation premium styling */
  .tab-navigation-premium {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    -webkit-backdrop-filter: blur(25px);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
  }
  
  /* Animation utilities */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  .animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out forwards;
  }
  
  .animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
  }
  
  .animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
  }
}

/* Utility classes */
@layer utilities {
  /* Text direction utilities */
  .text-start {
    text-align: start;
  }
  
  .text-end {
    text-align: end;
  }
  
  /* Margin utilities for RTL */
  .ms-auto {
    margin-inline-start: auto;
  }
  
  .me-auto {
    margin-inline-end: auto;
  }
  
  /* Padding utilities for RTL */
  .ps-4 {
    padding-inline-start: 1rem;
  }
  
  .pe-4 {
    padding-inline-end: 1rem;
  }
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced keyframe animations for glass effects */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes premiumGradient {
  0%, 100% { background-position: 0% 0%; }
  25% { background-position: 100% 0%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
}

@keyframes glassShimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes floatGlass {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    filter: blur(0px);
  }
  25% {
    transform: translateY(-10px) rotate(1deg);
    filter: blur(0.5px);
  }
  50% {
    transform: translateY(-20px) rotate(0deg);
    filter: blur(1px);
  }
  75% {
    transform: translateY(-10px) rotate(-1deg);
    filter: blur(0.5px);
  }
}

@keyframes pulseGlass {
  0%, 100% {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  50% {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .glass,
  .glass-card,
  .glass-button {
    background: white !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border: 1px solid #ccc !important;
  }
}
