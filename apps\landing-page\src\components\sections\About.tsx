import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useState, useEffect } from 'react';
import {
  HeartIcon,
  StarIcon,
  LightBulbIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

export default function About() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Icon mapping for values
  const iconMap = {
    trust: HeartIcon,
    quality: StarIcon,
    innovation: LightBulbIcon,
    community: UserGroupIcon,
  };

  // Get values from translation
  const values = Array.from({ length: 4 }, (_, i) => {
    const title = t(`about.values.${i}.title`);
    const description = t(`about.values.${i}.description`);
    
    // Map Arabic titles to icon keys
    const iconKey = title === 'الثقة' ? 'trust' :
                   title === 'الجودة' ? 'quality' :
                   title === 'الابتكار' ? 'innovation' :
                   title === 'المجتمع' ? 'community' :
                   title === 'Trust' ? 'trust' :
                   title === 'Quality' ? 'quality' :
                   title === 'Innovation' ? 'innovation' :
                   title === 'Community' ? 'community' : 'trust';
    
    return {
      title,
      description,
      icon: iconKey as keyof typeof iconMap,
    };
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="about"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(0, 122, 61, 0.08) 0%, transparent 60%),
          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Syrian-themed Glass Orbs */}
        <motion.div
          animate={{
            y: [-25, 25, -25],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-32 left-12 w-32 h-32 rounded-full opacity-12"
          style={{
            background: 'rgba(0, 122, 61, 0.1)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            border: '1px solid rgba(0, 122, 61, 0.2)',
            boxShadow: '0 8px 32px rgba(0, 122, 61, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [35, -35, 35],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 24, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-40 right-20 w-28 h-28 rounded-full opacity-15"
          style={{
            background: 'rgba(206, 17, 38, 0.1)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: '1px solid rgba(206, 17, 38, 0.2)',
            boxShadow: '0 8px 32px rgba(206, 17, 38, 0.1)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-18, 18, -18],
              x: [-9, 9, -9],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 7 + i * 1.8,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.6
            }}
            className="absolute w-1.5 h-1.5 bg-white rounded-full"
            style={{
              left: `${12 + (i * 9)}%`,
              top: `${20 + (i * 7)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="max-w-6xl mx-auto"
        >
          {/* Enhanced Header with Glass Effect */}
          <div className="text-center mb-20">
            <motion.div
              variants={itemVariants}
              className="relative mb-8"
            >
              <motion.h2
                className="heading-lg mb-6 text-white relative z-10 px-8 py-4 text-arabic-premium"
                initial={{ opacity: 0, y: 30, filter: 'blur(10px)' }}
                animate={{ opacity: 1, y: 0, filter: 'blur(0px)' }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
              >
                {t('about.title')}
              </motion.h2>

              {/* Glass backdrop for title */}
              <div
                className="absolute inset-0 -m-4 rounded-2xl opacity-25"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%)',
                  backdropFilter: 'blur(30px)',
                  WebkitBackdropFilter: 'blur(30px)',
                  border: '1px solid rgba(255, 255, 255, 0.12)',
                  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.08)'
                }}
              />
            </motion.div>

            <motion.p
              variants={itemVariants}
              className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed text-arabic px-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              {t('about.subtitle')}
            </motion.p>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-16">
            {/* Text Content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                {t('about.content')}
              </p>
              
              <div className="space-y-4">
                <div className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-6">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    {isRTL ? 'مهمتنا' : 'Our Mission'}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {t('about.mission')}
                  </p>
                </div>
                
                <div className="bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-6">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    {isRTL ? 'رؤيتنا' : 'Our Vision'}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {t('about.vision')}
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Visual Element */}
            <motion.div
              variants={itemVariants}
              className="relative"
            >
              <div className="bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-2xl p-8 lg:p-12">
                {/* Syrian Flag Colors Accent */}
                <div className="flex items-center justify-center mb-8">
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <div className="w-4 h-16 bg-syrian-red rounded"></div>
                    <div className="w-4 h-16 bg-syrian-white border border-gray-200 rounded"></div>
                    <div className="w-4 h-16 bg-syrian-black rounded"></div>
                    <div className="w-4 h-16 bg-syrian-green rounded"></div>
                  </div>
                </div>
                
                <div className="text-center">
                  <h3 className="heading-sm mb-4 text-gray-900 dark:text-white">
                    {isRTL ? 'فخورون بهويتنا السورية' : 'Proud of Our Syrian Identity'}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {isRTL 
                      ? 'نعمل على تمكين المواهب السورية وربطها بالفرص العالمية'
                      : 'We work to empower Syrian talents and connect them with global opportunities'
                    }
                  </p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Values */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? 'visible' : 'hidden'}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {values.map((value, index) => {
              const IconComponent = iconMap[value.icon];
              
              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="text-center group"
                >
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  
                  <h3 className="heading-sm mb-3 text-gray-900 dark:text-white">
                    {value.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                    {value.description}
                  </p>
                </motion.div>
              );
            })}
          </motion.div>

          {/* CTA */}
          <motion.div
            variants={itemVariants}
            initial="hidden"
            animate={inView ? 'visible' : 'hidden'}
            className="text-center mt-16"
          >
            <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 lg:p-12">
              <h3 className="heading-md mb-4 text-gray-900 dark:text-white">
                {isRTL ? 'انضم إلى رحلتنا' : 'Join Our Journey'}
              </h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                {isRTL 
                  ? 'كن جزءاً من قصة نجاح فريلا سوريا وساهم في بناء مستقبل أفضل للعمل الحر في سوريا'
                  : 'Be part of Freela Syria\'s success story and contribute to building a better future for freelancing in Syria'
                }
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <button className="btn-primary text-lg px-8 py-4">
                  {isRTL ? 'ابدأ رحلتك' : 'Start Your Journey'}
                </button>
                <button className="btn-secondary text-lg px-8 py-4">
                  {isRTL ? 'تعرف على الفريق' : 'Meet the Team'}
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
