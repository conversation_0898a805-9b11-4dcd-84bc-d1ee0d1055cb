[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts": "18"}, {"size": 14710, "mtime": 1749600836091, "results": "19", "hashOfConfig": "20"}, {"size": 9016, "mtime": 1749596942009, "results": "21", "hashOfConfig": "20"}, {"size": 385, "mtime": 1749589496137, "results": "22", "hashOfConfig": "20"}, {"size": 12492, "mtime": 1749600855961, "results": "23", "hashOfConfig": "20"}, {"size": 21668, "mtime": 1749600046512, "results": "24", "hashOfConfig": "20"}, {"size": 13041, "mtime": 1749598961324, "results": "25", "hashOfConfig": "20"}, {"size": 13776, "mtime": 1749598503640, "results": "26", "hashOfConfig": "20"}, {"size": 16230, "mtime": 1749601727154, "results": "27", "hashOfConfig": "20"}, {"size": 15978, "mtime": 1749600583678, "results": "28", "hashOfConfig": "20"}, {"size": 18978, "mtime": 1749601646623, "results": "29", "hashOfConfig": "20"}, {"size": 14628, "mtime": 1749599271491, "results": "30", "hashOfConfig": "20"}, {"size": 3319, "mtime": 1749593558895, "results": "31", "hashOfConfig": "20"}, {"size": 2575, "mtime": 1749598342857, "results": "32", "hashOfConfig": "20"}, {"size": 1646, "mtime": 1749589446276, "results": "33", "hashOfConfig": "20"}, {"size": 2677, "mtime": 1749597314087, "results": "34", "hashOfConfig": "20"}, {"size": 2428, "mtime": 1749596227387, "results": "35", "hashOfConfig": "20"}, {"size": 989, "mtime": 1749596212722, "results": "36", "hashOfConfig": "20"}, {"size": 2120, "mtime": 1749597019188, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ody2rz", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx", [], ["92"], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts", ["93", "94", "95", "96", "97", "98", "99", "100"], [], {"ruleId": "101", "severity": 1, "message": "102", "line": 37, "column": 7, "nodeType": "103", "messageId": "104", "endLine": 37, "endColumn": 20, "suggestions": "105", "suppressions": "106"}, {"ruleId": "101", "severity": 1, "message": "102", "line": 7, "column": 30, "nodeType": "103", "messageId": "104", "endLine": 7, "endColumn": 43}, {"ruleId": "101", "severity": 1, "message": "102", "line": 8, "column": 29, "nodeType": "103", "messageId": "104", "endLine": 8, "endColumn": 41}, {"ruleId": "101", "severity": 1, "message": "102", "line": 37, "column": 5, "nodeType": "103", "messageId": "104", "endLine": 37, "endColumn": 18}, {"ruleId": "107", "severity": 1, "message": "108", "line": 37, "column": 31, "nodeType": "109", "messageId": "110", "endLine": 37, "endColumn": 34, "suggestions": "111"}, {"ruleId": "101", "severity": 1, "message": "102", "line": 44, "column": 5, "nodeType": "103", "messageId": "104", "endLine": 44, "endColumn": 17}, {"ruleId": "107", "severity": 1, "message": "108", "line": 44, "column": 30, "nodeType": "109", "messageId": "110", "endLine": 44, "endColumn": 33, "suggestions": "112"}, {"ruleId": "101", "severity": 1, "message": "102", "line": 73, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 73, "endColumn": 16}, {"ruleId": "101", "severity": 1, "message": "102", "line": 74, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 74, "endColumn": 15}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["113"], ["114"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["115", "116"], ["117", "118"], {"messageId": "119", "data": "120", "fix": "121", "desc": "122"}, {"kind": "123", "justification": "124"}, {"messageId": "125", "fix": "126", "desc": "127"}, {"messageId": "128", "fix": "129", "desc": "130"}, {"messageId": "125", "fix": "131", "desc": "127"}, {"messageId": "128", "fix": "132", "desc": "130"}, "removeConsole", {"propertyName": "133"}, {"range": "134", "text": "124"}, "Remove the console.error().", "directive", "", "suggestUnknown", {"range": "135", "text": "136"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "137", "text": "138"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "139", "text": "136"}, {"range": "140", "text": "138"}, "error", [1086, 1152], [1100, 1103], "unknown", [1100, 1103], "never", [1286, 1289], [1286, 1289]]