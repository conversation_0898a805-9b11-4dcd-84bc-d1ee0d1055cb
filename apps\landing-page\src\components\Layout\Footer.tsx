import Link from 'next/link';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { BriefcaseIcon, SparklesIcon, HeartIcon } from '@heroicons/react/24/outline';

export default function Footer() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Get footer links from translation
  const footerSections = [
    {
      title: t('footer.links.company.title'),
      links: Array.from({ length: 4 }, (_, i) => ({
        label: t(`footer.links.company.items.${i}.label`),
        href: t(`footer.links.company.items.${i}.href`),
      })),
    },
    {
      title: t('footer.links.services.title'),
      links: Array.from({ length: 4 }, (_, i) => ({
        label: t(`footer.links.services.items.${i}.label`),
        href: t(`footer.links.services.items.${i}.href`),
      })),
    },
    {
      title: t('footer.links.legal.title'),
      links: Array.from({ length: 4 }, (_, i) => ({
        label: t(`footer.links.legal.items.${i}.label`),
        href: t(`footer.links.legal.items.${i}.href`),
      })),
    },
  ];

  const socialLinks = [
    {
      name: 'Facebook',
      href: t('footer.social.facebook'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      ),
    },
    {
      name: 'Twitter',
      href: t('footer.social.twitter'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
        </svg>
      ),
    },
    {
      name: 'LinkedIn',
      href: t('footer.social.linkedin'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      ),
    },
    {
      name: 'Instagram',
      href: t('footer.social.instagram'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
        </svg>
      ),
    },
  ];

  return (
    <footer
      className="relative overflow-hidden text-white"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(206, 17, 38, 0.12) 0%, transparent 60%),
          radial-gradient(circle at ${100 - mousePosition.x}% ${100 - mousePosition.y}%, rgba(0, 122, 61, 0.10) 0%, transparent 60%),
          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large Glass Orbs with Syrian Colors */}
        <motion.div
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 180, 360],
            scale: [1, 1.05, 1]
          }}
          transition={{ duration: 25, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 left-10 w-28 h-28 rounded-full opacity-15"
          style={{
            background: 'rgba(206, 17, 38, 0.08)',
            backdropFilter: 'blur(15px)',
            WebkitBackdropFilter: 'blur(15px)',
            border: '1px solid rgba(206, 17, 38, 0.15)',
            boxShadow: '0 8px 32px rgba(206, 17, 38, 0.08)'
          }}
        />

        <motion.div
          animate={{
            y: [30, -30, 30],
            rotate: [360, 180, 0],
            scale: [1.05, 1, 1.05]
          }}
          transition={{ duration: 30, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-40 right-20 w-20 h-20 rounded-full opacity-12"
          style={{
            background: 'rgba(0, 122, 61, 0.08)',
            backdropFilter: 'blur(12px)',
            WebkitBackdropFilter: 'blur(12px)',
            border: '1px solid rgba(0, 122, 61, 0.15)',
            boxShadow: '0 8px 32px rgba(0, 122, 61, 0.08)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-15, 15, -15],
              x: [-8, 8, -8],
              opacity: [0.1, 0.25, 0.1],
              scale: [1, 1.15, 1]
            }}
            transition={{
              duration: 12 + i * 3,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 1
            }}
            className="absolute w-1.5 h-1.5 bg-white rounded-full"
            style={{
              left: `${15 + (i * 15)}%`,
              top: `${25 + (i * 10)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>

      <div className="container mx-auto container-padding relative z-10">
        {/* Enhanced Main Footer Content */}
        <div className="py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12">
            {/* Enhanced Brand Section */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Link href="/" className="flex items-center space-x-3 rtl:space-x-reverse mb-8 group">
                  <motion.div
                    className="w-12 h-12 rounded-xl flex items-center justify-center relative overflow-hidden"
                    style={{
                      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%)',
                      backdropFilter: 'blur(20px)',
                      WebkitBackdropFilter: 'blur(20px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                    }}
                    whileHover={{ scale: 1.05, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <BriefcaseIcon className="w-7 h-7 text-white group-hover:scale-110 transition-transform duration-300" />
                    <SparklesIcon className="w-4 h-4 text-white absolute -top-1 -right-1 animate-pulse" />
                  </motion.div>
                  <span className="text-3xl font-bold text-white text-arabic-premium">
                    {isRTL ? 'فريلا سوريا' : 'Freela Syria'}
                  </span>
                </Link>

                <p className="text-white/80 mb-8 max-w-md leading-relaxed text-lg text-arabic">
                  {t('footer.description')}
                </p>

                {/* Enhanced Social Links */}
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={social.name}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 group"
                      style={{
                        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                        backdropFilter: 'blur(15px)',
                        WebkitBackdropFilter: 'blur(15px)',
                        border: '1px solid rgba(255, 255, 255, 0.15)',
                        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
                      }}
                      whileHover={{
                        scale: 1.1,
                        y: -2,
                        background: `rgba(${index === 0 ? '206, 17, 38' : index === 1 ? '0, 122, 61' : '124, 58, 237'}, 0.2)`
                      }}
                      whileTap={{ scale: 0.95 }}
                      aria-label={social.name}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.1 + (index * 0.1) }}
                    >
                      <div className="text-white group-hover:scale-110 transition-transform duration-300">
                        {social.icon}
                      </div>
                    </motion.a>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Enhanced Footer Links */}
            {footerSections.map((section, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 + (index * 0.1) }}
                className="p-6 rounded-2xl"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)',
                  backdropFilter: 'blur(15px)',
                  WebkitBackdropFilter: 'blur(15px)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.05)'
                }}
              >
                <h3 className="font-bold text-white mb-6 text-lg text-arabic-premium">
                  {section.title}
                </h3>
                <ul className="space-y-4">
                  {section.links.map((link, linkIndex) => (
                    <motion.li
                      key={linkIndex}
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Link
                        href={link.href}
                        className="text-white/70 hover:text-white transition-all duration-300 text-arabic hover:underline decoration-white/30 underline-offset-4"
                      >
                        {link.label}
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Enhanced Bottom Footer */}
        <div
          className="py-8 mt-8"
          style={{
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)'
          }}
        >
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="flex items-center gap-2"
            >
              <p className="text-white/60 text-sm text-arabic">
                {t('footer.copyright')}
              </p>
              <HeartIcon className="w-4 h-4 text-red-400 animate-pulse" />
            </motion.div>

            <motion.div
              className="flex items-center space-x-8 rtl:space-x-reverse text-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              {[
                { href: '/terms', label: isRTL ? 'شروط الاستخدام' : 'Terms of Service' },
                { href: '/privacy', label: isRTL ? 'سياسة الخصوصية' : 'Privacy Policy' },
                { href: '/cookies', label: isRTL ? 'سياسة الكوكيز' : 'Cookie Policy' }
              ].map((item) => (
                <motion.div
                  key={item.href}
                  whileHover={{ y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link
                    href={item.href}
                    className="text-white/60 hover:text-white transition-all duration-300 text-arabic hover:underline decoration-white/30 underline-offset-4"
                  >
                    {item.label}
                  </Link>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
}
